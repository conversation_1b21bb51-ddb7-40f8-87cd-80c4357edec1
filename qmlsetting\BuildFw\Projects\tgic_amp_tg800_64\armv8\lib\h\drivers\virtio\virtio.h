/*
 * Copyright (c) 2014 <PERSON>
 *
 * Use of this source code is governed by a MIT-style
 * license that can be found in the LICENSE file or at
 * https://opensource.org/licenses/MIT
 */
#pragma once

#include <os_stddef.h>
#include <os_assert.h>
#include <os_list.h>
#include <stdbool.h>
#include <sys/types.h>
#include <virtio/virtio_ring.h>
#include <os_spinlock.h>

/* virtio begin */
#define VIRTIO_NET_ID 1
#define VIRTIO_BLK_ID 2
#define VIRTIO_RPMSG_ID 7

#define VIRTIO_RX_INDEX 0 // 0 for amp driver, 1 for amp device
#define VIRTIO_CACHE_ENABLE 1 // default 1, disable for no cache

#define VIRTIO_IPI_IRQCTR 7

#define VIRTIO_BLK_IRQ 20
#define VIRTIO_RPMSG_IRQ 10
#define VIRTIO_NET_IRQ 0

#define SHM_BASE_PHYS  (0x6C000000)
#define SHM_BASE_SIZE  (0x04000000)

#define VIRTIO_NET_DEFAULT_SINGLE_SIZE      ((0x200000)*5*5) //(0x200000)
#define VIRTIO_NET_DEFAULT_NUM              1             //5
#define VIRTIO_NET_SIZE                     (VIRTIO_NET_DEFAULT_SINGLE_SIZE * VIRTIO_NET_DEFAULT_NUM)

#define VIRTIO_BLK_SIZE                     (0x200000)

// #define VIRTIO_RPMSG_DEFAULT_SINGLE_SIZE    (0x1400000)
// #define VIRTIO_RPMSG_DEFAULT_NUM            1
#define VIRTIO_RPMSG_DEFAULT_SINGLE_SIZE    (0x200000)
#define VIRTIO_RPMSG_DEFAULT_NUM            5
#define VIRTIO_RPMSG_SIZE                   (VIRTIO_RPMSG_DEFAULT_SINGLE_SIZE * VIRTIO_RPMSG_DEFAULT_NUM)

#define VIRTIO_SHMFS_SIZE    (0x200000)

#define VIRTIO_NET_QUEUE_NUM 1024//256
#define VIRTIO_BLK_QUEUE_NUM 256
#define VIRTIO_RPMSG_QUEUE_NUM 256
// #define VIRTIO_RPMSG_QUEUE_NUM 16384

#define VIRTIO_AMP_IRQ_SIZE 0x1000  //irq controller emulate
#define VIRTIO_AMP_CONFIG_SIZE 0x1000  //virtio device config status keep
#define VIRTIO_NET_OFFSET (VIRTIO_AMP_IRQ_SIZE)
#define VIRTIO_BLK_OFFSET (VIRTIO_NET_SIZE + VIRTIO_AMP_IRQ_SIZE)
#define VIRTIO_RPMSG_OFFSET (VIRTIO_NET_SIZE + VIRTIO_BLK_SIZE + VIRTIO_AMP_IRQ_SIZE)
#define VIRTIO_SHMFS_OFFSET (VIRTIO_RPMSG_OFFSET + VIRTIO_RPMSG_SIZE)

#define VIRTIO_DEVICE_ENABLE  1
#define VIRTIO_DEVICE_DISABLE 0

/* virtio end */


#define MAX_VIRTIO_RINGS 4

struct virtio_mmio_config;

#define EVENT_FLAG_SET (1 << 0)
#define EVENT_FLAG_RX  (1 << 0)
#define EVENT_FLAG_TX  (1 << 1)
#define EVENT_FLAG_IO  (1 << 2)

#define VIRTIO_POLL_IRQ 1 // 1 for irq, 2 for timer
#define VIRTIO_POLL_TIMER 2 // 1 for irq, 2 for timer

struct virtio_device {
    bool valid;

    uint index;
    uint irq;
    os_spinlock_t lock;

    volatile struct virtio_mmio_config *mmio_config;
    void *config_ptr;
    uint rx_index;  // 0 for amp driver, 1 for amp device
    uint poll_mode; // 1 for irq, 2 for timer
    uint irq_ctrl_id; //irq controller id
    uint cache_enable;
    void *vbase;
    uint vsize;
    os_paddr_t phybase;
    uint physize;
    uint queue_num;

    void *priv; /* a place for the driver to put private data */

    void (*virtio_notify)(struct virtio_device *dev, uint ring);
    void (*irq_driver_callback)(struct virtio_device *dev, uint ring);
    void (*config_change_callback)(struct virtio_device *dev);

    /* virtio rings */
    uint32_t active_rings_bitmap;
    struct vring ring[MAX_VIRTIO_RINGS];
};


struct os_virtio_params{
    os_paddr_t shm_addr;
    uint32_t   shm_length;
};


/* api used by devices to interact with the virtio bus */
int virtio_alloc_ring(struct virtio_device *dev, void *base, uint index, uint16_t len);

/* add a descriptor at index desc_index to the free list on ring_index */
void virtio_free_desc(struct virtio_device *dev, uint ring_index, uint16_t desc_index);

/* allocate a descriptor off the free list, 0xffff is error */
// uint16_t virtio_alloc_desc(struct virtio_device *dev, uint ring_index);
struct vring_desc *virtio_alloc_desc(struct virtio_device *dev, uint ring_index , uint16_t *index);
struct vring_desc *virtio_alloc_avail_desc(struct virtio_device *dev, uint ring_index , uint16_t *index);
struct vring_desc *virtio_alloc_used_desc(struct virtio_device *dev, uint ring_index , uint16_t *index);

/* allocate a descriptor chain the free list */
struct vring_desc *virtio_alloc_desc_chain(struct virtio_device *dev, uint ring_index, size_t count, uint16_t *start_index);

static inline struct vring_desc *virtio_desc_index_to_desc(struct virtio_device *dev, uint ring_index, uint16_t desc_index) {
    return (desc_index == 0xffff)?0:(&dev->ring[ring_index].desc[desc_index]);
}

void virtio_dump_desc(const struct vring_desc *desc);

/* submit a chain to the avail list */
void virtio_submit_chain(struct virtio_device *dev, uint ring_index, uint16_t desc_index);

void virtio_kick(struct virtio_device *dev, uint ring_idnex);

void virtio_vring_disable_cb(struct virtio_device *dev, uint ring_idx);

void virtio_vring_enable_cb(struct virtio_device *dev, uint ring_idx);

int virtio_ampdev_init(uint dev_id, uint rx_index, uint irq, void *vbase, uint vsize, uint queue_num, uint poll_mode, uint cache_enable);
#ifdef VIRTIO_AMP_MODE
void* virtio_get_buff_addr(void *base, uint16_t len, uint ring_index, uint16_t idx, uint buff_size);
void virtio_submit_used_chain(struct virtio_device *dev, uint ring_index, uint16_t desc_index, uint32_t len);
uint virtio_get_rx_index(struct virtio_device *dev);
uint virtio_get_tx_index(struct virtio_device *dev);
#endif

os_paddr_t virtio_get_shm_addr(void);
void virtio_mmio_irq(void *arg);
void virtio_status_irq(void *arg);
void os_virtio_param_init(struct os_virtio_params *param);


