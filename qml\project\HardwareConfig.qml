﻿
/******************************************************************************
 * Copyright 2023-2023 Three Gorges Intelligent Industrial Control Technology Co., Ltd.
 * All right reserved. See COPYRIGHT for detailed Information.
 *
 * @file       HardwareConfig.qml
 * @brief      硬件配置页面
 *
 * <AUTHOR>
 * @date       2023/10/21
 * @history
 *****************************************************************************/
import QtQuick 2.15
import QtQuick.Controls 2.15
import Qt.labs.platform 1.1
import QtQuick.Dialogs 1.3 as Dia
import "qrc:/qml/control/common"
import "qrc:/qml/control/tab"
import "qrc:/qml/gv"

//硬件配置页面
Rectangle {
    id: control
    width: 700
    height: 500
    radius: 5

    property string deviceName: GlobalVariable.activedDevice

    property real t0: 1
    property int t1: 1
    property int t2: 1
    property int t3: 1
    property int t4: 1
    property int t5: 1

    property string ip
    property string port

    property var cyclicTasks
    property var executionOrder
    property var debuggingInterface

    //标题行
    QkButtonRow {
        id: title
        QkLabel {
            anchors.left: parent.left
            anchors.leftMargin: 20
            anchors.verticalCenter: parent.verticalCenter

            text: qsTr("Hardware Configuration") + (trans ? trans.transString : "")
        }
        QkLabel {
            text: qsTr(
                      "Current Device") + (trans ? trans.transString : "") + ":"
            anchors.right: title_currdevice.left
            anchors.rightMargin: 10
            anchors.verticalCenter: parent.verticalCenter
        }
        QkLabel {
            id: title_currdevice
            text: control.deviceName
            anchors.right: parent.right
            anchors.rightMargin: 20
            anchors.verticalCenter: parent.verticalCenter
        }
    }

    QkTabView {
        id: tab
        anchors.top: title.bottom
        anchors.topMargin: 5
        height: parent.height - 110
        width: parent.width
        model: ["Cyclic Tasks", "Execution Order", "Debugging Interface"]
        Component.onCompleted: {
            cyclicTasks = c_cyclicTasks.createObject(tab.stackLayout, {})

            executionOrder = c_executionOrder.createObject(tab.stackLayout, {})

            debuggingInterface = c_debuggingInterface.createObject(
                        tab.stackLayout, {})
        }
    }
    
    Component {
        id: c_cyclicTasks
        Rectangle {
            width: parent.width - 10
            height: parent.height

            //            border.width: 1
            //            border.color: "black"
            Rectangle {
                anchors.centerIn: parent
                width: parent.width - 40
                height: parent.height - 40
                border.width: 1
                border.color: "lightgray"

                Column {
                    anchors.centerIn: parent
                    spacing: 15
                    Repeater {
                        model: listModel
                        Row {
                            spacing: 20
                            QkLabel {
                                width: 250
                                text: (index === 0 ? (model.name) : (model.name + "*T0")) + "(ms):"
                            }
                            QkTextField {
                                id: textField
                                readOnly: index === 0 ? false : true
                                //inputMask: "00"
                                validator: IntValidator {
                                    bottom: 1
                                    top: index === 0 ? 50 : 500000
                                }
                                Binding {
                                    id: textBinding
                                    target: textField
                                    property: "text"
                                    value: index === 0 ? model.value : (model.value * t0)
                                }
                                
                                onTextChanged: {
                                    if (index === 0 && text.length > 0) {
                                        let val = listModel.get(0).value
                                        if(isNaN(val) || val === text)
                                            return

                                        t0 = parseFloat(text)
                                        if (t0 < 1)
                                            t0 = 1
                                        if (t0 > 50)
                                            t0 = 50
                                        listModel.setProperty(0, "value", t0.toString())
                                    }
                                }

                                onEditingFinished: {

                                    //                                    if (index === 0) {
                                    //                                        var value = text
                                    //                                        console.log("value", value)
                                    //                                        for (var i = 0; i < listModel.count; i++) {
                                    //                                            var newv = parseInt(
                                    //                                                        value) * Math.pow(2, i)
                                    //                                            listModel.setProperty(
                                    //                                                        i, "value",
                                    //                                                        newv.toString())
                                    //                                        }
                                    //                                    }
                                }
                            }
                            QkButton {
                                text: "+"
                                onClicked: {
                                    if (index === 0) {
                                        addT0(index)
                                    } else {
                                        addtime(index)
                                    }
                                }
                            }
                            QkButton {
                                text: "-"
                                onClicked: {
                                    if (index === 0) {
                                        reduceT0(index)
                                    } else {
                                        reducetime(index)
                                    }
                                }
                            }
                        }
                    }
                }
            }

            QkLabel {
                text: qsTr("Task Configuration") + (trans ? trans.transString : "")
                anchors.left: parent.left
                anchors.leftMargin: 50
                anchors.top: parent.top
                anchors.topMargin: 50
            }
            Component.onCompleted: {
                getCyclicTasksData()
            }
        }
    }
    ListModel {
        id: listModel
    }

    ListModel {
        id: listModel1
    }

    Component {
        id: c_executionOrder
        Rectangle {
            width: parent.width - 10
            height: parent.height
            Rectangle {
                anchors.centerIn: parent
                width: parent.width - 40
                height: parent.height - 40
                border.width: 1
                border.color: "lightgray"
                ListView {
                    id: listview
                    anchors.centerIn: parent
                    width: parent.width - 40
                    height: parent.height - 40
                    interactive: false
                    model: listModel1
                    clip: true
                    //纵向滚动条
                    ScrollBar.vertical: QkScrollBar {
                        id: vBar
                        z: 10
                        opacity: listview.contentHeight > listview.height ? 1.0 : 0
                        visible: opacity
                        active: visible
                        orientation: Qt.Vertical
                        stepSize: listview.height / listview.contentHeight / 2
                        size: 40 / listview.height
                        Component.onCompleted: {
                            minimumSize = Qt.binding(function () {
                                return 40 / listview.height
                            })
                        }
                    }
                    move: Transition {
                        NumberAnimation {
                            properties: "x,y"
                            duration: 100
                        }
                    }

                    delegate: Rectangle {
                        id: wrapper
                        width: listview.width
                        height: 40
                        color: "transparent"
                        MouseArea {
                            id: mousearea
                            anchors {
                                left: parent.left
                                leftMargin: 0
                                top: parent.top
                                topMargin: 0
                            }
                            width: parent.width - 20
                            height: parent.height
                            cursorShape: Qt.OpenHandCursor
                            acceptedButtons: Qt.LeftButton | Qt.MiddleButton
                            onWheel: {
                                var angle = wheel.angleDelta.y / 8
                                if (vBar.visible) {
                                    //console.log("onWheel down", control.currentIndex,dataObj.visibleCount, dataObj.selectedCount)
                                    if (angle > 0) {
                                        vBar.decrease()
                                    } else if (angle < 0) {
                                        vBar.increase()
                                    }
                                }
                            }
                            onClicked: {
                                listview.currentIndex = index
                            }
                            onPressed: {
                                wrapper.color = "skyblue"
                                mousearea.cursorShape = Qt.ClosedHandCursor
                            }
                            onReleased: {
                                wrapper.color = "transparent"
                                mousearea.cursorShape = Qt.OpenHandCursor
                            }
                            onMouseYChanged: {

                                var pore = listview.indexAt(
                                            mousearea.mouseX + wrapper.x,
                                            mousearea.mouseY + wrapper.y)
                                //console.log("index", index, "pore", pore)
                                //console.log("onMouseYChanged", pore,listModel1.count)
                                if (index !== pore && pore >= 0
                                        && pore < listModel1.count) {
                                    listModel1.move(index, pore, 1)
                                    //调整全部顺序
                                    for (var i = 0; i < listModel1.count; i++) {
                                        listModel1.setProperty(i,
                                                               "order", i + 1)
                                    }
                                }
                            }
                        }
                        QkLabel {
                            anchors.left: parent.left
                            anchors.leftMargin: 20
                            anchors.verticalCenter: parent.verticalCenter
                            text: order
                            width: 50
                            horizontalAlignment: Text.AlignLeft
                        }
                        Row {
                            anchors.left: parent.left
                            anchors.leftMargin: 50
                            anchors.verticalCenter: parent.verticalCenter
                            spacing: 80
                            QkLabel {
                                id: coll
                                text: qsTr("File") + (trans ? trans.transString : "") + ":  " + name
                                width: 200
                            }
                            ComboBox {
                                model: ["T1", "T2", "T3", "T4", "T5"]
                                currentIndex: valueToIndex(type)
                                onCurrentIndexChanged: {

                                    listModel1.setProperty(index, "type",
                                                           indexToValue(
                                                               currentIndex))
                                }
                            }
                        }
                        Rectangle {
                            anchors.bottom: wrapper.bottom
                            width: parent.width
                            height: 1
                            color: "black"
                        }
                    }
                }
                Component.onCompleted: {
                    getExecutionOrderData()
                }
            }
        }
    }

    Component {
        id: c_debuggingInterface
        Rectangle {
            width: parent.width - 10
            height: parent.height

            //            border.width: 1
            //            border.color: "black"
            property bool di_validate: ttt_ip.acceptableInput
                                       && txt_port.acceptableInput

            Rectangle {
                anchors.centerIn: parent
                width: parent.width - 40
                height: parent.height - 40
                border.width: 1
                border.color: "lightgray"

                Column {
                    spacing: 10
                    anchors.left: parent.left
                    anchors.leftMargin: 50
                    anchors.top: parent.top
                    anchors.topMargin: 50
                    Row {
                        spacing: 10
                        QkLabel {
                            width: 160
                            text: qsTr(
                                      "Network IP Address") + (trans ? trans.transString : "") + ":"
                        }
                        QkIPInput {
                            id: ttt_ip
                            text: control.ip
                            onIpeditingFinished: {
                                control.ip = newtext
                            }
                        }
                    }
                    Row {
                        spacing: 10
                        QkLabel {
                            width: 160
                            text: qsTr("Port") + (trans ? trans.transString : "") + ":"
                        }
                        QkTextField {
                            id: txt_port
                            width: 100
                            text: control.port
                            readOnly: false
                            validator: IntValidator {
                                bottom: 1000
                                top: 65534
                            }
                            onEditingFinished: {
                                control.port = text
                            }
                        }
                    }
                }
            }
            Component.onCompleted: {
                getDebuggingInterfaceData()
            }
        }
    }

    QkButton {
        isGradientBgColor: true
        text: qsTr("Save") + (trans ? trans.transString : "")
        anchors.right: parent.horizontalCenter
        anchors.rightMargin: 50
        anchors.bottom: parent.bottom
        anchors.bottomMargin: 30
        width: 150
        onClicked: {
            if (debuggingInterface.di_validate) {
                writeTasktoFile()
            } else {
                inputErrorDialog.open()
            }
        }
    }
    QkButton {
        text: qsTr("Close") + (trans ? trans.transString : "")
        anchors.left: parent.horizontalCenter
        anchors.leftMargin: 50
        anchors.bottom: parent.bottom
        anchors.bottomMargin: 30
        width: 150
        onClicked: {
            popupCenter.close()
        }
    }

    Dia.MessageDialog {
        id: inputErrorDialog
        informativeText: qsTr("Please check all the inputs") + (trans ? trans.transString : "")
        title: qsTr("Input Error") + (trans ? trans.transString : "")
        text: qsTr("Input Error") + (trans ? trans.transString : "")
        standardButtons: Dia.StandardButton.Ok
    }

    Dia.MessageDialog {
        id: saveCompleteDialog
        informativeText: qsTr(
                             "Save Complete") + (trans ? trans.transString : "")
        title: qsTr("Notification") + (trans ? trans.transString : "")

        standardButtons: Dia.StandardButton.Ok
    }

    Component.onCompleted: {
        tab.tabBar.currentIndex = 0
    }

    function getDebuggingInterfaceData() {
        var diobj = projectAndFileManage.getDebuggingInterfaceData(
                    control.deviceName)

        console.log("getDebuggingInterfaceData", JSON.stringify(diobj))
        control.ip = diobj["ip"]
        control.port = diobj["port"]
    }

    function getCyclicTasksData() {
        //从后台获取数据
        var jsonary = projectAndFileManage.getCyclicTasksData(
                    control.deviceName)
        console.log("getCyclicTasksData", JSON.stringify(jsonary))
        listModel.clear()
        for (var i = 0; i < jsonary.length; i++) {
            var obj = jsonary[i]
            listModel.append({
                                 "name": obj.name,
                                 "value": obj.value
                             })
        }
        gett0t5()
    }

    function writeTasktoFile() {
        //任务时间
        //console.log("writeTasktoFile", t0, t1, t2, t3, t4, t5)
        projectAndFileManage.modifyTaskData(control.deviceName, "T0",
                                            listModel.get(0).value)
        projectAndFileManage.modifyTaskData(control.deviceName, "T1",
                                            listModel.get(1).value)
        projectAndFileManage.modifyTaskData(control.deviceName, "T2",
                                            listModel.get(2).value)
        projectAndFileManage.modifyTaskData(control.deviceName, "T3",
                                            listModel.get(3).value)
        projectAndFileManage.modifyTaskData(control.deviceName, "T4",
                                            listModel.get(4).value)
        projectAndFileManage.modifyTaskData(control.deviceName, "T5",
                                            listModel.get(5).value)
        //通讯地址
        projectAndFileManage.modifyDebuggingInterfaceData(control.deviceName,
                                                          control.ip,
                                                          control.port)
        var jsonary = []
        for (var i = 0; i < listModel1.count; i++) {
            var row = listModel1.get(i)
            jsonary.push({
                             "name": row.name,
                             "type": row.type,
                             "order": i + 1
                         })
            //修改图形化文件
            if (row.name.indexOf(".FBD") > 0 || row.name.indexOf(".CFC") > 0
                    || row.name.indexOf(".SFC") > 0 || row.name.indexOf(
                        ".LD") > 0) {
                fbdManage.modifyTaskName(row.path, row.type)
            }
        }
        //console.log("modifyOrderData", JSON.stringify(jsonary))
        //传给后台更新
        projectAndFileManage.modifyOrderData(control.deviceName, jsonary)

        //toolbox.editFuncTWithSaveFile()
        //toolbox.readXml()
        gett0t5()

        //toolbox.editTime(t0.toString(),t1.toString(),t2.toString(),t3.toString(), t4.toString(),t5.toString())
        //console.log("control.ip", control.ip, control.port)
        //toolbox.editNetwork(control.ip, control.port)
        //toolbox.writeXml()
        saveCompleteDialog.open()
    }

    function getExecutionOrderData() {
        //从后台获取数据
        var jsonary = projectAndFileManage.getExecutionOrderData(
                    control.deviceName)
        //排序
        jsonary.sort(function (a, b) {
            return parseInt(a.order) - parseInt(b.order)
        })
        console.log("getExecutionOrderData", JSON.stringify(jsonary))
        listModel1.clear()
        for (var l = 0; l < jsonary.length; l++) {
            var obj = jsonary[l]
            listModel1.append({
                                  "name": obj.name,
                                  "type": obj.type,
                                  "order": obj.order,
                                  "path": obj.path
                              })
        }
    }

    function valueToIndex(value) {
        if (value === "T2")
            return 1
        if (value === "T3")
            return 2
        if (value === "T4")
            return 3
        if (value === "T5")
            return 4
        return 0
    }
    function indexToValue(index) {
        if (index === 1)
            return "T2"
        if (index === 2)
            return "T3"
        if (index === 3)
            return "T4"
        if (index === 4)
            return "T5"
        return "T1"
    }
    
    function addT0(index) {
        let val = parseFloat(listModel.get(0).value)
        if(isNaN(val)) return
        // console.log("addT0 t0", t0)
        t0 = parseFloat((val + 0.05).toFixed(2))
        if (t0 > 10)
            t0 = 10
        listModel.setProperty(0, "value", t0.toString())
    }

    function reduceT0(index) {
        let val = parseFloat(listModel.get(0).value)
        if(isNaN(val)) return
        //console.log("reduceT0 t0", t0)
        t0 = parseFloat((val - 0.05).toFixed(2))
        if (t0 < 1)
            t0 = 1
        listModel.setProperty(0, "value", t0.toString())
    }

    function addtime(index) {
        gett0t5()

        //console.log("t0-t5", t0, t1, t2, t3, t4, t5, "index", index)
        if (index === 1) {
            t1 = t1 * 2
        } else if (index === 2) {
            t2 = t2 * 2
        } else if (index === 3) {
            t3 = t3 * 2
        } else if (index === 4) {
            t4 = t4 * 2
        } else if (index === 5) {
            t5 = t5 * 2
        }
        checkt1t5()
        sett0t5()
    }

    function reducetime(index) {
        gett0t5()
        //console.log("t0-t5", t0, t1, t2, t3, t4, t5, "index", index)
        if (index === 1) {
            t1 = t1 / 2
        } else if (index === 2) {
            t2 = t2 / 2
        } else if (index === 3) {
            t3 = t3 / 2
        } else if (index === 4) {
            t4 = t4 / 2
        } else if (index === 5) {
            t5 = t5 / 2
        }
        checkt1t5()
        sett0t5()
    }

    function checkt1t5() {
        if (t1 < 2)
            t1 = 2
        if (t2 < t1 * 2)
            t2 = t1 * 2
        if (t3 < t2 * 2)
            t3 = t2 * 2
        if (t4 < t3 * 2)
            t4 = t3 * 2
        if (t5 < t4 * 2)
            t5 = t4 * 2
    }

    function gett0t5() {
        t0 = parseFloat(listModel.get(0).value)
        t1 = parseInt(listModel.get(1).value)
        t2 = parseInt(listModel.get(2).value)
        t3 = parseInt(listModel.get(3).value)
        t4 = parseInt(listModel.get(4).value)
        t5 = parseInt(listModel.get(5).value)
    }

    function sett0t5() {
        listModel.setProperty(0, "value", t0.toString())
        listModel.setProperty(1, "value", t1.toString())
        listModel.setProperty(2, "value", t2.toString())
        listModel.setProperty(3, "value", t3.toString())
        listModel.setProperty(4, "value", t4.toString())
        listModel.setProperty(5, "value", t5.toString())
    }
}
