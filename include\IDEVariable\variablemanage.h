﻿/*
 * @Author: liquan
 * @Date: 2024-03-27 13:57:19
 * @Last Modified by:   liquan
 * @Last Modified time: 2024-03-27 13:57:19
 */

#ifndef VARIABLEMANAGE_H
#define VARIABLEMANAGE_H

#include "fbpintemplate.h"
#include "fbreferencelist.h"
#include "fbtemplate.h"
#include "idev1_variable_global.h"
#include "keywordlist.h"
#include "monitorlist.h"
#include "variablelist.h"
#include "variabletype.h"
#include <QJsonArray>
#include <QJsonObject>
#include <QMap>
#include <QObject>
#include <QRegExp>
#include <QSqlDatabase>
#include <QStringList>
#include <QVector>

class IDEVARIABLE_EXPORT VariableManage : public QObject
{
    Q_OBJECT
public:
    static VariableManage &instance();
    // 初始化
    void init(QSqlDatabase &db);
    //设置应用路径
    void setAppPath(const QString &appath);
    /* 变量类型表 */
    // 根据设备名称获取变量类型表的所有数据 to compile
    Q_INVOKABLE QVector<QStringList> getVariabTypeVec(const QString &deviceName);
    // 根据设备名称获取变量类型表的所有数据
    Q_INVOKABLE QJsonArray getVariabTypeJsonArray(const QString &deviceName);
    // 获取设备名获取变量类型表的所有数据
    Q_INVOKABLE QJsonArray getDataType(const QString &deviceName);
    // 根据id获取变量类型表的相关数据
    VariableType getDataTypeByID(const long &id);
    Q_INVOKABLE QJsonArray getDataTypeList(const long &id);
    // 根据mainID获取变量类型表的所有数据
    Q_INVOKABLE QJsonArray getDataTypeListByMainID(const long &mainID);
    // 根据parentID获取变量类型表的子和子孙节点数据
    Q_INVOKABLE QJsonArray getDataTypeChildList(const long &mainID);
    //根据parentID获取变量类型表的节点数据
    QList<VariableType> getDataTypeChildListByParentID(const long &parentID);
    //根据名称
    VariableType getSTRUCTDataTypeByName(const QString &deviceName, const QString &name);
    // 往变量类型表添加主数据类型项
    Q_INVOKABLE bool addMainDataType(const QString &deviceName, const QString &name);
    // 往变量类型表添加子数据类型项
    Q_INVOKABLE bool addChildDataType(const QString &deviceName, const long &parentID, const QString &name);
    // 根据记录ID修改变量类型表子数据类型项
    Q_INVOKABLE bool modifyChildDataType(const long &id, const QString &type, const QString &name, const QString &shortName,
                                         const int &arraycount, const QString &description);
    //检查自引用
    Q_INVOKABLE bool checkSelfReference(const long &id, const QString &type);
    //检查交叉引用
    bool checkCrossReference(const long &id);

    // 根据记录ID删除变量类型表中的数据类型项
    Q_INVOKABLE bool deleteDataType(const long &id);
    // 根据设备名称删除变量类型表中设备的所有数据类型
    Q_INVOKABLE bool deleteDeviceDataType(const QString &deviceName);
    // 判断是否有子节点
    Q_INVOKABLE bool hasChild(const long &id);
    // 获取变量类型表所有数据
    Q_INVOKABLE QJsonArray getAllVariableType(const QString &deviceName);
    //根据结构体名称获取该结构体所有属性
    Q_INVOKABLE QJsonArray getVariableType(const QString &deviceName, const QStringList &nameList);

    QMap<QString, int> getAllStructTypeName(const QString &deviceName, const QString &name);

    QList<VariableType> getDataTypeListFromMainID(const long &mainID);
    //获取所有基础类型
    Q_INVOKABLE QStringList getBaseDataType();

    QList<VariableType> getBaseDataTypeList();
    //为导出数据服务的基础类型名称，排除了时间型、字符串型
    QStringList getBaseDataTypeNameForExport();
    //名称校验获取内置数据类型名称与用户定义名称
    QList<VariableType> getDataTypeFromSettingName(const QString &deviceName);
    //根据数据类型获取默认初始值
    QString getDefaultInitValueFromDataType(const QString &dataTypeName);
    //判断是否是基础型
    Q_INVOKABLE bool isBaseDataType(const QString &daName);

    /* 变量表 */
    // 获取引用了DateTypeID的所有变量名字
    Q_INVOKABLE QString getVariableNameByDataTypeID(const long &dataTypeID);

    // 导入指定文件中的变量表的数据
    Q_INVOKABLE QString importVariable(const QString devicename, const QString &filePath);
    // 导入全局变量
    Q_INVOKABLE QString importGlibalVariable(const QString devicename, const QString &filePath);

    // 根据id导出变量表中的数据
    Q_INVOKABLE bool exportVariableByID(const QStringList &ids, const QString &filePath, const QString &fileName);

    // 根据io数据进行导出
    Q_INVOKABLE bool exportVariableByIOData(const QJsonArray &IODataList, const QString &filePath);

    // 根据Scope\Type查找变量特定数据
    Q_INVOKABLE QJsonArray getAllList(const QString &deviceName, const QStringList &scope, const QStringList &type);
    //根据设备获取所有变量表内容，包含引用信息
    QList<VariableList> getVariableListFromSettingName(const QString &deviceName);


    // 根据设备名称获取变量表的所有数据
    Q_INVOKABLE QVector<QStringList> getVariableListVec(const QString &deviceName);
    // 根据设备名称获取变量表的唯一变量
    Q_INVOKABLE QVector<QStringList> getVariableListVecWithUnique(const QString &deviceName);
    // 根据设备名称获取被文件引用的变量信息
    Q_INVOKABLE QVector<QStringList> getVariableListVecWithFileReference(const QString &deviceName);
    // 根据设备名称和功能块名称获取该功能块定义变量
    Q_INVOKABLE QVector<QStringList> getVariableListVecWithFBDefine(const QString &deviceName, const QString &type,
                                                                    const QString &fbName);

    // FBReferenceList 输出 Owned ReferenceName InstanceName
    Q_INVOKABLE QVector<QStringList> getFBReferenceList(const QString &deviceName);
    //获取设备中的功能块引用信息，为环形引用检测服务
    QList<FBReferenceList> getFBReferenceListForCheckCircular(const QString &deviceName);

    // 根据设备、所有权和类型获取变量表数据
    Q_INVOKABLE QJsonArray getVariableList(const QString &deviceName, const QStringList &owned, const QStringList &type);

    Q_INVOKABLE QJsonArray getVariableListWithScopeAndOwned(const QString &deviceName, const QStringList &scope,
                                                            const QStringList &owned);

    QJsonArray getVariableListFromOwned(const QString &deviceName, const QString &owned);

    QList<VariableList> getIOMVariableListFromOwned(const QString &deviceName, const QString &owned);

    QList<VariableList> getIOMVariableListFromOwnedAndType(const QString &deviceName, const QString &owned, const QString &type);

    // 获取添加变量时下一个变量的起始地址
    Q_INVOKABLE double getNextVariableStartAddress(const QString &deviceName, const QString &owned, const QString &type);

    // 校验地址是否踩踏
    bool checkAddressTrample(const VariableList &currVar);

    // 将地址转换为double
    double addressToDouble(const QString &address, const QString &type);

    // 根据ID获取变量信息
    Q_INVOKABLE QJsonObject getVariableFromID(const long &varID);
    // 添加变量
    Q_INVOKABLE bool addVariable(const QString &deviceName, const QString &scope, const QString &owned, const QString &type,
                                 const QString &name, const QString &datatype, const long &dataTypeID, const QString &address);
    // 修改变量
    Q_INVOKABLE QString modifyVariable(const long &varID, const QString &scope, const QString &name,
                                       const QString &datatype, const long &dataTypeID, const int &arrayLength,
                                       const QString &address, const QString &initialValue, const bool &isRetained,
                                       QString description);
    // 修改变量 特供给modbus修改
    Q_INVOKABLE QString modifyVariable(const long &varID, const QString &scope, const QString &name,
                                       const QString &datatype, const long &dataTypeID, const int &arrayLength,
                                       const QString &address, const QString &initialValue, const bool &isRetained,
                                       QString description, const int &modbusAddress, const QString &ModbusRw,
                                       const int &segement, const int &offset);
    // 修改变量 ModbusAddress ModbusRW。
    Q_INVOKABLE bool modifyModbusaddressAndRW(const long &varID, const int &modbusaddress,
                                              const QString &modbusrw);
    // 根据deviceName和Owned、变量名修改 segement 和 offset的值。
    Q_INVOKABLE QString modifySegementAndOffset(const QString &deviceName, const QString &owned, const QString &name,
                                                const int &segement,
                                                const int &offset);
    Q_INVOKABLE bool deleteVariable(const long &varID);
    // 根据deviceName和Owned删除变量
    Q_INVOKABLE bool deleteVariableByOwned(const QString &deviceName, const QString &owned);
    // 根据deviceName和Name删除变量
    Q_INVOKABLE bool deleteVariableByName(const QString &deviceName, const QString &name);
    // 删除设备的所有变量
    Q_INVOKABLE bool deleteDeviceVariable(const QString &deviceName);
    // 引入变量
    Q_INVOKABLE bool quoteVariable(const long &sourceVarID, const QString &target_owned, const QString &target_type);
    // 传入一组变量ID和ModbusAddress起始地址，根据变量类型自动生成对应的每个变量的ModbusAddress的值。
    Q_INVOKABLE bool autoAssignModbusAddresses(const QList<long> &varIDs, int startingAddress);

    /* 监视表 */
    // 按照TableName字段升序获取所有数据
    Q_INVOKABLE QJsonArray getMonitorTableList(const QString &deviceName);
    // 获取监视表的TableName的去重后的所有字段
    Q_INVOKABLE QJsonArray getMonitorTableName(const QString &deviceName);
    // 根据设备名称获取监视表的所有数据
    Q_INVOKABLE QVector<QStringList> getMonitorVec(const QString &deviceName);
    // 获取监视表的所有数据
    Q_INVOKABLE QJsonArray getAllMonitorList();
    // 获取设备指定的监视表
    Q_INVOKABLE QJsonArray getMonitorList(const QString &deviceName, const QString &tableName);
    // 获取设备的监视变量信息
    QList<MonitorList> getMonitorList(const QString &deviceName);
    // 添加监视表
    Q_INVOKABLE bool addMonitorVariable(const QString &deviceName, const QString &tableName, const long &varID, QString monitorName,
                                        QString monitoredValue, QString displayFormat, int monitoringWithTrigger, int modifyWithTrigger,
                                        QString modifyValue, bool isModify, QString description, int state, QString scope, QString owned, QString type, QString dataType,
                                        const long &dataTypeID);
    // 删除监视表
    Q_INVOKABLE bool deleteMonitorVariable(const long &id);

    bool modifyMonitorVariable(MonitorList monitor);
    // 修改监视表
    Q_INVOKABLE bool modifyMonitorVariable(const long &id, const QString &deviceName, const QString &tableName, const long &varID,
                                           QString monitorName,
                                           QString monitoredValue, QString displayFormat, int monitoringWithTrigger, int modifyWithTrigger,
                                           QString modifyValue, bool isModify, QString description, int state);

    // 删除设备指定的监视表
    Q_INVOKABLE bool deleteDeviceMonitor(const QString &deviceName, const QString &tableName);
    // 删除设备的所有监视表
    Q_INVOKABLE bool deleteDeviceMonitor(const QString &deviceName);

    /* 强制表 */
    // 按照TableName字段升序获取所有数据
    Q_INVOKABLE QJsonArray getForcedTableList(const QString &deviceName);
    // 获取监视表的TableName的去重后的所有字段
    Q_INVOKABLE QJsonArray getForcedTableName(const QString &deviceName);
    // 根据设备名称获取强制表的所有数据
    Q_INVOKABLE QVector<QStringList> getForcedVec(const QString &deviceName);
    // 获取强制表的所有数据
    Q_INVOKABLE QJsonArray getAllForcedList();
    // 获取设备的所有强制表数据
    Q_INVOKABLE QJsonArray getForcedList(const QString &deviceName, const QString &tableName);
    // 添加强制表
    Q_INVOKABLE bool addForcedVariable(const QString &deviceName, const QString &tableName, const long &varID,
                                       const QString &forceName, const QString &monitoredValue,
                                       const QString &displayFormat, const int &monitoringWithTrigger,
                                       const QString &forcedValue, const int &isForced, QString description,
                                       const int &state, QString scope, QString owned, QString type, QString dataType,
                                       const long &dataTypeID);
    // 删除强制表
    Q_INVOKABLE bool deleteForcedVariable(const long &id);
    // 删除设备指定的强制表
    Q_INVOKABLE bool deleteDeviceForced(const QString &deviceName, const QString &tableName);
    // 删除设备的所有强制表
    Q_INVOKABLE bool deleteDeviceForced(const QString &deviceName);
    // 修改强制表
    Q_INVOKABLE bool modifyForcedVariable(const long &id, const QString &deviceName, const QString &tableName,
                                          const long &varID, const QString &forceName, const QString &monitoredValue,
                                          const QString &displayFormat, const int &monitoringWithTrigger,
                                          const QString &forcedValue, const int &isForced, QString description,
                                          const int &state);

    // 功能块部分数据库操作逻辑
    // 根据设备名称以及作用域和文件分类来获取功能块列表
    Q_INVOKABLE QJsonArray getFunctionBlockList(const QString &deviceName, const QStringList &owned, const QStringList &type);
    // 添加功能块
    Q_INVOKABLE bool addFunctionBlock(const QString &deviceName, const QString &scope, const QString &owned, const QString &type,
                                      const QString &referenceName, const QString &instanceName, const QString &fbType);
    // 修改功能块
    Q_INVOKABLE bool modifyFunctionBlock(const long &id, const QString &referenceName);
    // 删除功能块
    Q_INVOKABLE bool deleteFunctionBlock(const long &id);

    Q_INVOKABLE QString conversion(const QString value, const QString fromValue, const QString toValue);
    // 根据SettingName和Owned删除引用
    Q_INVOKABLE bool deleteFbreferenceSettingName(const QString &settingName, const QString &owned);

    Q_INVOKABLE bool deleteFbreferenceFromID(const long &id);

    QList<FBReferenceList> getFBReferenceListFromSetteingName(const QString &settingName);

    // 实现函数，根据传入的deviceName，修改所有deviceName和闯入值相同的行的monitoringWithTrigger为0, forcedValue为空
    Q_INVOKABLE bool resetDeviceForced(const QString &deviceName);

    // 根据SettingName和instanceName删除引用表中的数据
    Q_INVOKABLE bool deleteFbreferenceListData(const QString &settingName, const QString &instanceName);


    int getBitLengthFromName(const QString &settingName, const QString &name);

    Q_INVOKABLE bool readFile2VariableType(const QString &fileName);

    Q_INVOKABLE bool readVariableType2File(const QString &fileName);

    Q_INVOKABLE bool checkFBAndFUNCName(const QString &name);

    //添加功能块
    Q_INVOKABLE bool addFBTemplate(const QString &deviceName, const QString &owned,
                                   const QString &fbType,
                                   const QString &childType, const QString &fbName,
                                   const QString &description, const QString &version, const QString &cCode, const QString &oFile, const QString &aFile,
                                   const QString &soFile);
    //修改功能块
    Q_INVOKABLE QString modifyFBTemplate(const long &id, const QString &deviceName, const QString &owned,
                                         const QString &fbType,
                                         const QString &childType, const QString &fbName,
                                         const QString &description, const QString &version, const QString &cCode, const QString &oFile, const QString &aFile,
                                         const QString &soFile);

    //添加功能块引脚
    Q_INVOKABLE bool addFBPinTemplate(const QString &fbName, const QString &scope, const QString &pinName,
                                      int sortnumber, const QString &direction, const QString &dataType,
                                      const QString &initValue, const QString &defaultValue, int arraylength,
                                      const QString &description);
    //修改功能块引脚
    Q_INVOKABLE QString modifyFBPinTemplate(const long &id, const QString &fbName, const QString &scope,
                                            const QString &pinName, const QString &direction, const QString &dataType,
                                            const QString &initValue, const QString &defaultValue, int arraylength,
                                            const QString &description);
    //从功能块名称获取引脚信息(包含static)
    QList<FBPinTemplate> getFBPinTemplateFromFBName(const QString &fbName);

    QList<FBPinTemplate> getFBPinTemplateWithoutStaticFromFBName(const QString &fbName);

    //获取内部功能或功能块定义 功能块树数据源 fbType=FUNCTION/FUNCTIONBLOCK/ADVANCE
    Q_INVOKABLE QJsonArray getLibFunctionAndBlock(const QString &fbType);

    QList<FBTemplate> getLibFunctionAndBlockList();

    QList<KeywordList> getKeywordList();

    //检查变量名称语法 主要判断数组写法，结构体写法 暂不判断变量作用域
    Q_INVOKABLE bool checkVariableNameSyntax(const QString &deviceName, const QString &varname);

    //获取所有功能或功能块定义
    Q_INVOKABLE QJsonObject getAllFunctionAndBlock();

    //获取所有内置块定义
    QVector<QStringList> getAllLibFBVec();
    //获取所有内置块代码
    QVector<QStringList> getAllLibFBCodeVec();
    //获取所有块的结构体类型定义
    QVector<QStringList> getAllLibDataTypeVec();
    //从CSV文件中导入所有的内部功能块 代码 和引脚信息
    void importLibFBFromCSV(QString codefilePath, QString pinfilePath);

    QMap<QString, int> m_bitLengthMap;

signals:
    //变量变化
    void variableChanged(QString devicename, QString owned, QString action, QString name);
    //dataType数据类型变化
    void dataTypeChanged(QString devicename, QString action, QString name);
    //监视表内容变化
    void monitorChanged(QString devicename, QString tablename, QString action, QString name);
    //强制表内容变化
    void forcedChanged(QString devicename, QString action, QString name);
    //功能块引用变化
    void fbreferenceChanged(QString devicename, QString action, QString name);

private:
    VariableManage()
    {
        m_bitLengthMap.insert("BOOL", 8);
        m_bitLengthMap.insert("BYTE", 8);
        m_bitLengthMap.insert("CHAR", 8);
        m_bitLengthMap.insert("SINT", 8);
        m_bitLengthMap.insert("USINT", 8);
        m_bitLengthMap.insert("INT", 16);
        m_bitLengthMap.insert("UINT", 16);
        m_bitLengthMap.insert("DINT", 32);
        m_bitLengthMap.insert("UDINT", 32);
        m_bitLengthMap.insert("REAL", 32);
        m_bitLengthMap.insert("LREAL", 64);
        m_bitLengthMap.insert("DWORD", 32);
        m_bitLengthMap.insert("WORD", 16);
        m_bitLengthMap.insert("TIME", 32);
        m_bitLengthMap.insert("DATE", 32);
        m_bitLengthMap.insert("TIME OF DAY", 32);
        m_bitLengthMap.insert("DATE AND TIME", 32);
        m_bitLengthMap.insert("LINT", 64);
        m_bitLengthMap.insert("LWORD", 64);
        m_bitLengthMap.insert("ULINT", 64);
    }

    QRegExp nameExpr = QRegExp("^[a-zA-Z][a-zA-Z0-9_]{1,29}$");

    //根据类型名称获取该类型的bit长度

    //递归算bitlength
    bool modifyParentBitLength(const long &id);
    //递归算mainoffset
    bool modifyMainOffset(const long &id, const int startindex);
    //交叉引用检查缓存
    QList<QString> m_tempDataTypeListFromCheckReference;

    template <typename T>
    bool insert_tableData(T &);

    template <typename T>
    bool update_tableData(T &a_tableData);

    template <typename T>
    bool get_Table_ByID(T &, const long &);

    template <typename T>
    bool get_Table_ByAQMap(T &, QMap<QString, QVariant> &);

    bool get_variable_types(const QString &type, QVector<QSharedPointer<VariableType>> &all_variable_types);

    bool compareVariableType(const QSharedPointer<VariableType> &temp, const QSharedPointer<VariableType> &targ);

    template <typename T>
    bool deleteTable_by_query(qx_query &query);

    QSqlDatabase m_db;
    //应用目录
    QString appDir;
};

#endif // VARIABLEMANAGE_H
