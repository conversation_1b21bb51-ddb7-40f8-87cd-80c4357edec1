#ifndef __PF_PACKET_H__
#define __PF_PACKET_H__

#include <os_types.h>
#include <os_mq.h>
#include <os_sem.h>
#include <os_list.h>
#include <sys/socket.h>
#include <sys/time.h>
#ifdef OS_USING_HRTIMER
#include <timer/hrtimer.h>
#endif /* OS_USING_HRTIMER */

#ifdef OS_USING_NET_DEVICE
#include <net_dev.h>
#else
#error "please select Drivers > NET > Using NET DEVICE"
#endif /* OS_USING_NET_DEVICE */

typedef struct
{
    os_slist_node_t       packet_sock_list_node;
    int                   type;     /* socket type */
    int                   protocol; /* socket protocol */
    int                   if_index; /* posix net/if.h if_index */
    struct os_net_device *net_dev;  /* net device */
    int                   fcntl_flag;
    int                   shutdown_flag;
    os_bool_t             tx_capture;        /* is enable capture tx data */
    size_t                recv_total_len;    /* total length of received data */
    size_t                recv_max_len;      /* max length of received data */
    os_msgqueue_id        packet_mq;         /* packet recv message queue */
    os_semaphore_id       packet_sem;        /* packet recv semaphore */
#ifdef OS_USING_HRTIMER
    os_hrtimer_t          recv_timeout_timer; /* receive timeout with timer */
#endif /* OS_USING_HRTIMER */
    // os_tick_t             recv_timeout_tick; /* receive timeout with ticks */
    struct timeval        recv_timeout_tv;   /* receive timeout with timeval */

} packet_sock_t;

#ifdef __cplusplus
extern "C" {
#endif

packet_sock_t *packet_socket(int domain, int type, int protocol);
int            packet_closesocket(packet_sock_t *packet_sock);
int            packet_shutdown(packet_sock_t *packet_sock, int how);
int            packet_bind(packet_sock_t *packet_sock, const struct sockaddr *name, socklen_t namelen);
int            packet_listen(packet_sock_t *packet_sock, int backlog);                               /* not support */
int            packet_accept(packet_sock_t *packet_sock, struct sockaddr *addr, socklen_t *addrlen); /* not support */
int     packet_connect(packet_sock_t *packet_sock, const struct sockaddr *name, socklen_t namelen);  /* not support */
ssize_t packet_sendto(packet_sock_t         *packet_sock,
                      const void            *data,
                      size_t                 size,
                      int                    flags,
                      const struct sockaddr *to,
                      socklen_t              tolen);
ssize_t packet_send(packet_sock_t *packet_sock, const void *data, size_t size, int flags); /* not support */
ssize_t packet_recvfrom(packet_sock_t   *packet_sock,
                        void            *mem,
                        size_t           len,
                        int              flags,
                        struct sockaddr *from,
                        socklen_t       *fromlen);
ssize_t packet_recv(packet_sock_t *packet_sock, void *mem, size_t len, int flags);
int     packet_getsockopt(packet_sock_t *packet_sock, int level, int optname, void *optval, socklen_t *optlen);
int     packet_setsockopt(packet_sock_t *packet_sock, int level, int optname, const void *optval, socklen_t optlen);
int     packet_ioctlsocket(packet_sock_t *packet_sock, long cmd, void *argp);
int     packet_getsockname(packet_sock_t *packet_sock, struct sockaddr *name, socklen_t *namelen); /* not support */

#ifdef __cplusplus
}
#endif

#endif /* __PF_PACKET_H__ */
