#ifndef __VIRTIO_NET_AMP_H__
#define __VIRTIO_NET_AMP_H__

#include <stdlib.h>
#include <inttypes.h>
#include <assert.h>
#include <string.h>
#include <os_spinlock.h>
#include <os_timer.h>
#include <os_event.h>
#include <net_dev.h>
#include <os_list.h>
#include <os_types.h>
#include <os_memory.h>
#include <virtio.h>

#define VIRTIO_NET_F_CSUM                   (1<<0)
#define VIRTIO_NET_F_GUEST_CSUM             (1<<1)
#define VIRTIO_NET_F_CTRL_GUEST_OFFLOADS    (1<<2)
#define VIRTIO_NET_F_MTU                    (1<<3)
#define VIRTIO_NET_F_MAC                    (1<<5)
#define VIRTIO_NET_F_GSO                    (1<<6) // removed in v1.3
#define VIRTIO_NET_F_GUEST_TSO4             (1<<7)
#define VIRTIO_NET_F_GUEST_TSO6             (1<<8)
#define VIRTIO_NET_F_GUEST_ECN              (1<<9)
#define VIRTIO_NET_F_GUEST_UFO              (1<<10)
#define VIRTIO_NET_F_HOST_TSO4              (1<<11)
#define VIRTIO_NET_F_HOST_TSO6              (1<<12)
#define VIRTIO_NET_F_HOST_ECN               (1<<13)
#define VIRTIO_NET_F_HOST_UFO               (1<<14)
#define VIRTIO_NET_F_MRG_RXBUF              (1<<15)
#define VIRTIO_NET_F_STATUS                 (1<<16)
#define VIRTIO_NET_F_CTRL_VQ                (1<<17)
#define VIRTIO_NET_F_CTRL_RX                (1<<18)
#define VIRTIO_NET_F_CTRL_VLAN              (1<<19)
#define VIRTIO_NET_F_GUEST_ANNOUNCE         (1<<21)
#define VIRTIO_NET_F_MQ                     (1<<22)
#define VIRTIO_NET_F_CTRL_MAC_ADDR          (1<<23)
#define VIRTIO_NET_F_HASH_TUNNEL            (1ULL<<51)
#define VIRTIO_NET_F_VQ_NOTF_COAL           (1ULL<<52)
#define VIRTIO_NET_F_NOTF_COAL              (1ULL<<53)
#define VIRTIO_NET_F_GUEST_USO4             (1ULL<<54)
#define VIRTIO_NET_F_GUEST_USO6             (1ULL<<55)
#define VIRTIO_NET_F_HOST_USO               (1ULL<<56)
#define VIRTIO_NET_F_HASH_REPORT            (1ULL<<57)
#define VIRTIO_NET_F_GUEST_HDRLEN           (1ULL<<59)
#define VIRTIO_NET_F_RSS                    (1ULL<<60)
#define VIRTIO_NET_F_RSC_EXT                (1ULL<<61)
#define VIRTIO_NET_F_STANDBY                (1ULL<<62)
#define VIRTIO_NET_F_SPEED_DUPLEX           (1ULL<<63)

#define VIRTIO_NET_S_LINK_UP                (1<<0)
#define VIRTIO_NET_S_ANNOUNCE               (1<<1)

#define TX_RING_SIZE 32
#define RX_RING_SIZE 32

#define MAX_TX_PACKAGE_SIZE      (2048 * 5)
#define ETHERNET_FRAME_OVERHEAD  (32)      // maybe 18 is enough, 22 byte when use vlan
#define VIRTIO_RESERVE_HEAD      (sizeof(struct virtio_net_hdr) - 2)
#define VIRTIO_NET_MSS           (MAX_TX_PACKAGE_SIZE - ETHERNET_FRAME_OVERHEAD - VIRTIO_RESERVE_HEAD)

#define MAX_TX_MTU               (VIRTIO_NET_MSS)

#define VIRTIO_NET_MAX_INTERFACE            10

struct virtio_net_config {
    uint8_t mac[6];
    uint16_t status;
    uint16_t max_virtqueue_pairs;
    uint16_t mtu;
    uint32_t speed;
    uint8_t  duplex;
    uint8_t  rss_max_key_size;
    uint16_t rss_max_indirection_table_length;
    uint32_t supported_hash_types;
    uint32_t supported_tunnel_types;
};
// STATIC_ASSERT(sizeof(struct virtio_net_config) == 28);

struct virtio_net_hdr {
#define VIRTIO_NET_HDR_F_NEEDS_CSUM 1
#define VIRTIO_NET_HDR_F_DATA_VALID 2
#define VIRTIO_NET_HDR_F_RSC_INFO   4
    uint8_t  flags;
#define VIRTIO_NET_HDR_GSO_NONE     0
#define VIRTIO_NET_HDR_GSO_TCPV4    1
#define VIRTIO_NET_HDR_GSO_UDP      3
#define VIRTIO_NET_HDR_GSO_TCPV6    4
#define VIRTIO_NET_HDR_GSO_UDP_L4   5
#define VIRTIO_NET_HDR_GSO_ECN      0x80
    uint8_t  gso_type;
    uint16_t hdr_len;
    uint16_t gso_size;
    uint16_t csum_start;
    uint16_t csum_offset;
    uint16_t num_buffers; // unused in tx

    // Only if VIRTIO_NET_HASH_REPORT negotiated
    //uint32_t hash_value;
    //uint16_t hash_report;
    //uint16_t padding_reserved;
};
// STATIC_ASSERT(sizeof(struct virtio_net_hdr) == 12);

struct virtio_net_dev {
    struct virtio_device *dev;
    bool started;
    struct virtio_net_config *config;
    os_spinlock_t lock;
    os_event_dummy_t rx_event;
    struct os_list_node completed_rx_queue;
    int last_sbuf;
};

struct _napi_net_device {
    struct os_net_device  net_dev;
    struct napi_struct napi;
    os_timer_id timer;
    // os_list_node_t list;
    os_err_t (*enable_irq)(struct _napi_net_device *vdev, int enable);
};

struct os_dev_virtio_net {
    // struct os_net_device  net_dev;
    struct _napi_net_device napi_ndev;
    struct virtio_net_dev vi_ndev;
    uint8_t mac[6];
    uint32_t index;
    char name[50];
    os_task_id status_monitor;
    os_semaphore_dummy_t status_sem;
    os_list_node_t node;
};

struct os_virtio_net_info{
    const char *name;
    os_paddr_t  shm_addr;
    uint        shm_len;
    int32_t     task_cpuid;
};

struct os_virtio_net_params{
    bool net_status;
    uint poll_mode;
    uint vnet_num;
    struct os_virtio_net_info *vnet_info;
};

#define VNET_MEM_END(begin, size)				(begin + size -1)

int virtio_net_create_interface_simple(const char *name);
int virtio_net_create_interface(const char *name, os_paddr_t phy_mem_base, uint32_t phy_mem_size);
int virtio_net_destory_interface(const char *name);

void os_virtio_net_param_init(struct os_virtio_net_params *param);

#endif
