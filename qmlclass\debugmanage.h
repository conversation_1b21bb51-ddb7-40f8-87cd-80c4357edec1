﻿#ifndef DEBUGMANAGE_H
#define DEBUGMANAGE_H

#include "IDEVariable/variablelist.h"
#include "IDEVariable/variabletype.h"
#include <QJsonDocument>
#include <QJsonObject>
#include <QJsonParseError>
#include <QMutex>
#include <QObject>
#include <QSharedPointer>
#include <QStack>
#include <QTextStream>

class VarTemp : public QObject
{
    Q_OBJECT
  public:
    VarTemp(){};
    long id;                   // ID
    QString settingName;       // 配置名称
    QString scope;             // 变量作用域
    QString owned;             // 文件名
    QString type;              // 文件分类
    QString name;              // 变量名称
    QString dataType;          // 数据类型
    long dataTypeID;           // 数据类型ID
    int arrayLength;           // 长度
    QString address;           // 地址
    bool isConstant;           // 是否是常量
    bool isOpc;                // 是否是OPC特有项目
    bool isRetained;           // 是否在保持寄存器中
    QString description;       // 注释
    QString createTime;        // 创建时间
    QString lastModifyTime;    // 上一次修改时间
    QString varList;           // 所属变量表
    bool isShow;               // 是否显示
    QString initialValue;      // 初始值
    bool isSelected;           // 是否选中
    int state;                 // 变量状态
    int modbusAddress;         // Modbus子站对外地址 范围0-65535
    QString modbusRw;          // Modbus子站对外读写 N=不可读写 R=只读 W=可读写
    int segement;              // 段号
    int offset;                // 偏移量
    int masterNo;              // IO变量主站号
    int slaveNo;               // IO变量子站号
    int slotNo;                // IO变量槽位号
    QString modelNumber;       // IO变量模块号
    int priority;              // IO变量优先级1-5
    bool isRedundancy;         // 是否冗余
    QString StructMainName;    // 结构体主名称
    QString StructParentName;  // 结构体父名称
    QString StructName;        // 结构体属性名称
    QString StructDataType;    // 结构体数据类型名称
    int StructTotalBitLength;  // 结构体属性bit长度
    int StructParentBitOffset; // 结构体属性bit位偏移
};

class DebugManage : public QObject
{
    Q_OBJECT
public:
    static DebugManage &instance();
    //初始化实例赋值当前应用目录
    void init(const QString &appPath);
    //打开项目
    void OpenProject(const QString &pName, const QString &pPath);
    //修改变量 、编译后清空对应数据
    Q_INVOKABLE void clear();
    //读取编译后生成的监控变量表和数据结构定义文件
    Q_INVOKABLE int readFile();

    // 形成树形结构的数据为选取变量表格服务
    void genVarListForSelect();

    // 同步当前已注册的变量的VID属性值
    Q_INVOKABLE void syncSelectedVariableID(const QString &deviceNamedeviceName);

    QString getVIDFromVarNameInVarListForSelect(const QJsonArray &ary, const QString &varName);

    Q_INVOKABLE QJsonArray getVariableListFromScopeAndOwned(const QString &deviceName, const QStringList &scope,
                                                            const QString &owned);
    Q_INVOKABLE QJsonArray getVariableListFromScopeAndType(const QString &deviceName, const QString &scope, const QString &type);

    Q_INVOKABLE QJsonArray getChildVariableData(int id, const QString &vid, const QString &datatype);

    QJsonArray getTypeList(int id, const QString &vid, const QString &varname, const QString &dataType, int deep);

    QJsonArray getChildTypeList(int id, const QString &vid, const QString &varname, const QString &dataType, int deep, int structid);

    bool haveChild(const QString &dataType, int parentid);

private:
    //应用目录
    QString appDir;
    //当前项目名称
    QString projectName;
    //当前项目目录
    QString projectDir;

    // 从原始文件中读到的变量数据
    QList<QSharedPointer<VarTemp>> debugVarList;
    // 从原始文件中读到的结构体数据
    QMap<QString, QList<VariableType>> debugDataTypeMap;
    // 为选取变量功能的UI表格准备的数据格式
    QList<QJsonObject> varListForSelect;

    int varChildIndex = 0;

};

#endif // DEBUGMANAGE_H
