#ifndef __KERNEL_CONFIG_H__
#define __KERNEL_CONFIG_H__

/* Generated by <PERSON>configlib (https://github.com/ulfalizer/Kconfiglib) */

/* arch */

#define OS_ARCH_CPU_64BIT
#define OS_ARCH_ARM
#define ARCH_ARMV8
#define AARCH64
#define ARM64_CPU_CORTEX_A55

/* GIC */

#define ARCH_ARM_GICV3
/* end of GIC */
/* end of arch */

/* Kernel */

#define OS_NAME_MAX_15
#define OS_NAME_MAX 15
#define OS_USING_FPU
#define OS_FPU_USE_CHECK

/* SMP */

#define OS_USING_SMP
#define OS_SMP_MAX_CPUS 4
#define OS_SCHED_STRATEGY_SKIP_IPI_SET
#define OS_SCHED_SECONDARY_FOUND
/* end of SMP */

/* Task */

#define OS_TASK_PRIORITY_256
#define OS_TASK_PRIORITY_MAX 256
/* end of Task */

/* Timer */

#define OS_USING_KERNEL_TIMER
#define OS_USING_HASH_BUCKET_TIMER
#define OS_HASH_BUCKET_TIMER_POWER 3
/* end of Timer */
#define OS_USING_WORKQUEUE
#define OS_USING_SYSTEM_WORKQUEUE

/* Log */

#define OS_USING_KERNEL_LOG
#define KLOG_GLOBAL_LEVEL_WARNING
#define KLOG_GLOBAL_LEVEL 1
#define KLOG_USING_COLOR
#define KLOG_WITH_FUNC_LINE
/* end of Log */

/* KLog */

#define OS_USING_KLOG
#define OS_KLOG_RB_DESC_COUNT_BITS 12
#define OS_KLOG_TEXT_SIZE_BITS 8
#define OS_KLOG_TEXT_MAX_SIZE 512
#define KLOG_GLOBAL_LEVEL_WARNING1
#define KLOG_GLOBAL_LEVEL1 4
#define KLOG_WITH_FUNC_LINE1
/* end of KLog */

/* LockDep */

/* end of LockDep */

/* Debug */

/* Check */

#define OS_USING_TASK_HOOK
#define OS_USING_PARAMETER_CHECK
#define OS_USING_SET_ERRNO
/* end of Check */

/* CPU monitor */

#define OS_USING_CPU_MONITOR
/* end of CPU monitor */

/* Memory monitor */

/* end of Memory monitor */

/* CPU profiler */

/* end of CPU profiler */

/* Stack trace */

#define STACK_TRACE_EN
#define EXC_DUMP_STACK
#define TASK_STACK_OVERFLOW_STACK_SIZE 256
#define CALL_BACK_TRACE_MAX_DEPTH 10
/* end of Stack trace */
/* end of Debug */

/* IPC */

#define OS_USING_MUTEX
#define OS_USING_SPINLOCK
#define OS_USING_TICKET_SPINLOCK
#define OS_USING_SEMAPHORE
#define OS_USING_EVENT
#define OS_USING_MESSAGEQUEUE
#define OS_USING_MAILBOX
/* end of IPC */

/* Memory */

#define OS_USING_HEAP
#define OS_USING_HEAP_DLMALLOC
#define OS_USING_MEM_POOL
/* end of Memory */

/* Module */

/* end of Module */
/* end of Kernel */

/* Debug */

#define OS_DEBUG
#define LOG_BUFF_SIZE_256
#define OS_LOG_BUFF_SIZE 256
/* end of Debug */

/* C standard library adapter */

#define OS_USING_LIBC_ADAPTER
#define OS_USING_MUSL_ADAPTER
#define OS_USING_IO_MULTIPLEXING
#define PRESET_FD_SETSIZE 64
#define OS_USING_FS_AIO
#define OS_USING_FS_NAIOC 8
#define OS_AIO_WORKQUEUE_PRIORITY 15
#define OS_AIO_WORKQUEUE_STACK_SIZE 4096
/* end of C standard library adapter */

/* Drivers */

#define OS_USING_DEVICE
#define OS_USING_DEVICE_NOTIFY

/* BLOCK */

#define OS_USING_BLOCK
/* end of BLOCK */

/* Cache */

#define BSP_CACHE_LINE_SIZE 32
/* end of Cache */

/* CAN */

/* end of CAN */

/* CONSOLE */

#define OS_USING_CONSOLE
/* end of CONSOLE */

/* DMA */

#define OS_USING_DMA
#define OS_USING_DMA_RAM
/* end of DMA */

/* EEPROM */

/* end of EEPROM */

/* FAL */

/* end of FAL */

/* I2C */

/* end of I2C */

/* MISC */

/* end of MISC */

/* MTD */

/* end of MTD */

/* NAND */

/* end of NAND */

/* NET */

#define OS_USING_NET_DEVICE
#define OS_USING_NET_NAPI

/* protocol support */

#define OS_NET_PROTOCOL_LWIP
/* end of protocol support */
/* end of NET */

/* PIN */

#define OS_USING_PIN
/* end of PIN */

/* RTC */

/* end of RTC */

/* SDIO */

/* end of SDIO */

/* Sensors */

/* end of Sensors */

/* Serial */

#define OS_USING_SERIAL
#define OS_SERIAL_DELAY_CLOSE

/* posix serial */

/* end of posix serial */

/* rtt uart */

/* end of rtt uart */
/* end of Serial */

/* SFLASH */

/* end of SFLASH */

/* SPI */

/* end of SPI */

/* Timer */

#define OS_USING_TIMER_DRIVER
#define OS_USING_CLOCKSOURCE
#define OS_CLOCKSOURCE_SHOW
#define OS_USING_TIMEKEEPING
#define OS_USING_CLOCKEVENT
#define OS_CLOCKEVENT_SHOW
#define OS_USING_HRTIMER
/* end of Timer */

/* VIRTIO */

#define OS_USING_VIRTIO
#define OS_USING_VIRTIO_RPMSG
#define OS_USING_VIRTIO_NET
#define OS_USING_VIRTIO_BLK
#define OS_USING_VIRTIO_BLK_SD
#define OS_USING_VIRTIO_BLK_SD_TEST
#define OS_USING_VIRTIO_SHMFS
#define VIRTIO_AMP_MODE
/* end of VIRTIO */

/* WDG */

/* end of WDG */
/* end of Drivers */

/* Components */

/* BModem */

/* end of BModem */

/* Compress */

/* Zlib */

/* end of Zlib */
/* end of Compress */

/* GDB */

#define OS_USING_GDB
#define OS_USING_GDB_TASK
/* end of GDB */

/* Dlog */

#define OS_USING_DLOG
#define DLOG_PRINT_LVL_I
#define DLOG_GLOBAL_PRINT_LEVEL 6
#define DLOG_COMPILE_LVL_D
#define DLOG_COMPILE_LEVEL 7
#define DLOG_USING_FILTER
#define DLOG_USING_ASYNC_OUTPUT
#define DLOG_ASYNC_OUTPUT_BUF_SIZE 8388608
#define DLOG_ASYNC_OUTPUT_TASK_STACK_SIZE 16384
#define DLOG_ASYNC_OUTPUT_TASK_PRIORITY 160

/* Log format */

#define DLOG_WITH_FUNC_LINE
#define DLOG_OUTPUT_TIME_INFO
#define DLOG_TIME_USING_TIMESTAMP
/* end of Log format */

/* Dlog backend option */

#define DLOG_BACKEND_USING_FILESYSTEM
#define DLOG_FILE_DIR "/user/dlog/"
#define DLOG_FILE_NAME "tgic.log"
#define DLOG_FILE_SIZE 20971520
#define DLOG_FILE_NUM 5
#define DLOG_FILE_ENABLE_CACHE
#define DLOG_FILE_USING_CACHE 1
#define DLOG_FILE_CACHE_BUF_SIZE 1024
/* end of Dlog backend option */
/* end of Dlog */

/* EDR */

/* end of EDR */

/* FileSystem */

#define OS_USING_VFS
#define VFS_FILESYSTEM_TYPES_MAX 6
#define OS_USING_VFS_DEVFS
#define OS_USING_VFS_FATFS_KERNEL

/* Elm-ChaN's FatFs, generic FAT filesystem module */

#define OS_VFS_FAT_CODE_PAGE 437
#define OS_VFS_FAT_USE_LFN_3
#define OS_VFS_FAT_USE_LFN 3
#define OS_VFS_FAT_MAX_LFN 255
#define OS_VFS_FAT_DRIVES 2
#define OS_VFS_FAT_MAX_SECTOR_SIZE 4096
#define OS_VFS_FAT_REENTRANT
/* end of Elm-ChaN's FatFs, generic FAT filesystem module */
#define OS_USING_VFS_LFS_KERNEL

/* Little filesystem module */

#define LFS_NAME_MAX 255
#define LFS_FILE_MAX 2147483647
#define LFS_THREADSAFE
#define LFS_CONFIG_ASSERT
/* end of Little filesystem module */
/* end of FileSystem */

/* GUI */

#define OS_GUI_DISP_DEV_NAME "lcd"
#define OS_GUI_INPUT_DEV_NAME "touch"
/* end of GUI */

/* iperf3 */

#define OS_USING_IPERF3_CFG
#define CFG_IPERF3_RUN_TASK_PRIORITY 11
/* end of iperf3 */

/* Network */

/* TCP/IP */

/* LwIP */

#define NET_USING_LWIP
#define NET_USING_LWIP212
#define LWIP_IPV6_FORWARD 1
#define LWIP_USING_IGMP
#define LWIP_USING_ICMP
#define LWIP_USING_DNS
#define LWIP_USING_DNS_LOCAL_HOSTLIST
#define LWIP_USING_DNS_LOCAL_HOSTLIST_IS_DYNAMIC
#define LWIP_USING_UDP
#define LWIP_USING_TCP
#define LWIP_USING_RAW
#define LWIP_REASSEMBLY_FRAG
#define LWIP_NETIF_STATUS_CALLBACK 1
#define LWIP_NETIF_LINK_CALLBACK 1
#define SO_REUSE 1
#define LWIP_SO_RCVTIMEO 1
#define LWIP_SO_SNDTIMEO 1
#define LWIP_SO_RCVBUF 1
#define LWIP_MULTIPLE_PHYSICAL_ETHERNET_CARD_SUPPORT
#define LWIP_ENABLE_MIP 0
#define LWIP_HAVE_LOOPIF 0
#define LWIP_NETIF_LOOPBACK 0
#define LWIP_USING_PING
#define LWIP_USING_IPERF_TCP
#define LWIP_ENABLE_APP
#define LWIP_ENABLE_ETHERNET_APP
/* end of LwIP */
/* end of TCP/IP */

/* Protocols */

/* CoAP */

/* end of CoAP */

/* FTP */

#define NET_USING_FTP
#define NET_USING_SIMPLE_FTP_SERVER
#define FTP_PASSIVE_MODE_ENABLE
#define FTP_USER_NAME_DEFAULT "user"
#define FTP_USER_PASS_DEFAULT "user"
#define USE_PASSIVE_MODE 1
#define FTP_CLIENT_MAX 10
#define FTP_BG_TRANS_CONN 10
#define FTP_ENABLE_ANONYMOUS_LOGIN
#define FTP_CACHE_BUF_SIZE 32768
/* end of FTP */

/* HTTP */

/* httpclient-v1.1.0 */

/* end of httpclient-v1.1.0 */
/* end of HTTP */

/* MQTT */

/* end of MQTT */

/* NTP */

/* end of NTP */

/* TFTP */

#define NET_USING_TFTP
#define NET_USING_LWIP_TFTP

/* lwip-tftp-v2.2.0 */

#define LWIP_TFTP_ENABLE_WRITE_CACHE
#define LWIP_TFTP_WRITE_CACHE_LEN 4096
#define LWIP_TFTP_USING_CLIENT
#define LWIP_TFTP_USING_SERVER
/* end of lwip-tftp-v2.2.0 */
/* end of TFTP */
/* end of Protocols */

/* Socket */

#define NET_USING_BSD
#define BSD_USING_LWIP
/* end of Socket */
/* end of Network */

/* OpenAMP */

/* end of OpenAMP */

/* Ramdisk */

/* end of Ramdisk */

/* RPMSG */

#define OS_USING_RPMSG
#define OS_USING_RPMSG_DEMO
/* end of RPMSG */

/* Security */

/* end of Security */

/* Shell */

#define OS_USING_SHELL
#define SHELL_TASK_NAME "shell"
#define SHELL_TASK_PRIORITY 16
#define SHELL_TASK_STACK_SIZE 65536
#define SHELL_USING_HISTORY
#define SHELL_HISTORY_LINES 5
#define SHELL_USING_DESCRIPTION
#define SHELL_CMD_SIZE 80
#define SHELL_ARG_MAX 10
#define SHELL_C_USING
/* end of Shell */

/* Telnetd */

/* end of Telnetd */

/* TRACEPOINT */

/* end of TRACEPOINT */

/* Ymodem */

/* end of Ymodem */
/* end of Components */

/* Common */

#define OS_USING_AVL_TREE
/* end of Common */

/* OSAL */

/* Cpp11 */

/* end of Cpp11 */

/* Vxworks */

/* end of Vxworks */

/* Posix */

#define OS_USING_KERNEL_POSIX
#define PTHREAD_NUM_MAX 48
#define PTHREAD_SPINLOCK_MAX 8
#define PTHREAD_MQ_MAX 8
#define PTHREAD_KEY_MAX 8
/* end of Posix */
/* end of OSAL */

#endif /* __KERNEL_CONFIG_H__ */

